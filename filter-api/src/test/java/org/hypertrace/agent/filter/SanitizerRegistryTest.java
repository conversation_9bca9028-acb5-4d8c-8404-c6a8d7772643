/*
 * Copyright The Hypertrace Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package org.hypertrace.agent.filter;

import static org.junit.jupiter.api.Assertions.*;

import org.hypertrace.agent.filter.api.SensitiveDataSanitizer;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.Test;

class SanitizerRegistryTest {

  @AfterEach
  void tearDown() {
    // Clear system properties after each test
    System.clearProperty("ht.data.sanitization.enabled");
  }

  @Test
  void testGetSanitizer() {
    SensitiveDataSanitizer sanitizer = SanitizerRegistry.getSanitizer();
    assertNotNull(sanitizer);
    assertTrue(sanitizer instanceof DefaultSensitiveDataSanitizer);

    // Should return the same instance
    SensitiveDataSanitizer sanitizer2 = SanitizerRegistry.getSanitizer();
    assertSame(sanitizer, sanitizer2);
  }

  @Test
  void testIsSanitizationEnabled_Default() {
    // Default should be enabled
    assertTrue(SanitizerRegistry.isSanitizationEnabled());
  }

  @Test
  void testIsSanitizationEnabled_SystemProperty() {
    // Test disabling via system property
    System.setProperty("ht.data.sanitization.enabled", "false");
    assertFalse(SanitizerRegistry.isSanitizationEnabled());

    // Test enabling via system property
    System.setProperty("ht.data.sanitization.enabled", "true");
    assertTrue(SanitizerRegistry.isSanitizationEnabled());

    // Test case insensitive
    System.setProperty("ht.data.sanitization.enabled", "FALSE");
    assertFalse(SanitizerRegistry.isSanitizationEnabled());
  }
}
