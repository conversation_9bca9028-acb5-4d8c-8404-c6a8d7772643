/*
 * Copyright The Hypertrace Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package org.hypertrace.agent.filter;

import org.hypertrace.agent.filter.api.SensitiveDataSanitizer;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/** Registry for managing data sanitizers */
public class SanitizerRegistry {

  private static final Logger logger = LoggerFactory.getLogger(SanitizerRegistry.class);

  private SanitizerRegistry() {}

  private static volatile SensitiveDataSanitizer sanitizer;

  /** Get the sensitive data sanitizer instance */
  public static SensitiveDataSanitizer getSanitizer() {
    if (sanitizer == null) {
      synchronized (SanitizerRegistry.class) {
        if (sanitizer == null) {
          boolean enabled = isSanitizationEnabled();
          boolean addMarkers = isMarkersEnabled();
          sanitizer = new DefaultSensitiveDataSanitizer(enabled, addMarkers);
          logger.info(
              "Data sanitization initialized, enabled: {}, markers: {}", enabled, addMarkers);
        }
      }
    }
    return sanitizer;
  }

  /** Reset the sanitizer (for testing purposes) */
  public static void reset() {
    sanitizer = null;
  }

  /** Check if data sanitization is enabled */
  public static boolean isSanitizationEnabled() {
    String sanitizationEnabled = getProperty("ht.data.sanitization.enabled");
    return !"false".equalsIgnoreCase(sanitizationEnabled);
  }

  /** Check if detection markers are enabled */
  public static boolean isMarkersEnabled() {
    String markersEnabled = getProperty("ht.data.sanitization.markers.enabled");
    return "true".equalsIgnoreCase(markersEnabled);
  }

  private static String getProperty(String name) {
    return System.getProperty(name, System.getenv(name.replaceAll("\\.", "_").toUpperCase()));
  }
}
