/*
 * Copyright The Hypertrace Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package org.hypertrace.agent.filter;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.regex.Pattern;

import org.hypertrace.agent.filter.api.SensitiveDataSanitizer;
import org.hypertrace.agent.filter.config.DynamicSanitizationRuleManager;
import org.hypertrace.agent.filter.config.SanitizationConfig;
import org.hypertrace.agent.filter.config.SanitizationRule;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;

/** Dynamic sensitive data sanitizer that supports rule-based sanitization */
public class DynamicSensitiveDataSanitizer implements SensitiveDataSanitizer {

  private static final Logger logger = LoggerFactory.getLogger(DynamicSensitiveDataSanitizer.class);

  private final ObjectMapper objectMapper = new ObjectMapper();
  private final DefaultSensitiveDataSanitizer fallbackSanitizer;

  // 缓存编译后的正则表达式模式
  private final Map<String, Pattern> patternCache = new HashMap<>();

  public DynamicSensitiveDataSanitizer() {
    this.fallbackSanitizer = new DefaultSensitiveDataSanitizer();
  }

  @Override
  public String sanitize(String input, Map<String, String> headers) {
    if (input == null || input.isEmpty()) {
      return input;
    }

    // 获取动态规则管理器实例
    DynamicSanitizationRuleManager ruleManager = DynamicSanitizationRuleManager.getInstance();

    // 如果规则管理器未初始化或配置未启用，使用默认的脱敏器
    if (ruleManager == null) {
      return fallbackSanitizer.sanitize(input, headers);
    }

    SanitizationConfig config = ruleManager.getCurrentConfig();
    System.out.println("[HT-Agent] config: " + config);
    if (config == null
        || !config.isEnabled()
        || config.getRules() == null
        || config.getRules().isEmpty()) {
      return fallbackSanitizer.sanitize(input, headers);
    }

    try {
      String contentType = getContentType(headers);

      if (isJsonContent(contentType)) {
        return sanitizeJson(input, config, headers);
      } else if (isXmlContent(contentType)) {
        return sanitizeXml(input, config, headers);
      } else if (isFormUrlEncoded(contentType)) {
        return sanitizeFormData(input, config, headers);
      } else {
        return sanitizeByPatterns(input, config, headers);
      }
    } catch (Exception e) {
      logger.warn("Error in dynamic sanitization, falling back to default: {}", e.getMessage());
      return fallbackSanitizer.sanitize(input, headers);
    }
  }

  @Override
  public boolean shouldSanitize(String contentType) {
    DynamicSanitizationRuleManager ruleManager = DynamicSanitizationRuleManager.getInstance();
    if (ruleManager == null) {
      return fallbackSanitizer.shouldSanitize(contentType);
    }

    SanitizationConfig config = ruleManager.getCurrentConfig();
    if (config == null || !config.isEnabled()) {
      return fallbackSanitizer.shouldSanitize(contentType);
    }

    // 检查是否有适用于该内容类型的规则
    if (config.getRules() != null) {
      for (SanitizationRule rule : config.getRules()) {
        if (rule.isEnabled() && isRuleApplicable(rule, contentType)) {
          return true;
        }
      }
    }

    // 如果没有动态规则，使用默认逻辑
    return fallbackSanitizer.shouldSanitize(contentType);
  }

  /** JSON 数据脱敏 */
  private String sanitizeJson(String json, SanitizationConfig config, Map<String, String> headers)
      throws Exception {
    JsonNode rootNode = objectMapper.readTree(json);
    sanitizeJsonNode(rootNode, "", config, headers);
    return objectMapper.writeValueAsString(rootNode);
  }

  /** 递归处理 JSON 节点 */
  private void sanitizeJsonNode(
      JsonNode node, String path, SanitizationConfig config, Map<String, String> headers) {
    if (node.isObject()) {
      ObjectNode objectNode = (ObjectNode) node;
      Iterator<Map.Entry<String, JsonNode>> fields = objectNode.fields();

      while (fields.hasNext()) {
        Map.Entry<String, JsonNode> field = fields.next();
        String fieldName = field.getKey();
        JsonNode fieldValue = field.getValue();

        if (fieldValue.isValueNode()) {
          // 应用字段名称规则
          SanitizationRule matchedRule = findMatchingFieldRule(fieldName, config);
          if (matchedRule != null) {
            String sanitizedValue = applySanitizationRule(fieldValue.asText(), matchedRule, config);
            objectNode.put(fieldName, sanitizedValue);
          } else if (fieldValue.isTextual()) {
            // 应用模式规则
            String sanitizedValue = sanitizeByPatterns(fieldValue.asText(), config, headers);
            if (!sanitizedValue.equals(fieldValue.asText())) {
              objectNode.put(fieldName, sanitizedValue);
            }
          }
        } else if (!fieldValue.isValueNode()) {
          // 递归处理嵌套对象和数组
          sanitizeJsonNode(fieldValue, path + "." + fieldName, config, headers);
        }
      }
    } else if (node.isArray()) {
      for (JsonNode element : node) {
        sanitizeJsonNode(element, path + "[]", config, headers);
      }
    }
  }

  /** XML 数据脱敏 */
  private String sanitizeXml(String xml, SanitizationConfig config, Map<String, String> headers) {
    String result = xml;

    // 应用字段名称规则
    for (SanitizationRule rule : getEnabledRules(config, SanitizationRule.RuleType.FIELD_NAME)) {
      if (rule.getFieldNames() != null) {
        for (String fieldName : rule.getFieldNames()) {
          String pattern = "<" + fieldName + ">([^<]+)</" + fieldName + ">";
          String replacement = "<"
              + fieldName
              + ">"
              + applySanitizationRule("$1", rule, config)
              + "</"
              + fieldName
              + ">";
          result = result.replaceAll(pattern, replacement);
        }
      }
    }

    // 应用模式规则
    result = sanitizeByPatterns(result, config, headers);

    return result;
  }

  /** Form 数据脱敏 */
  private String sanitizeFormData(
      String formData, SanitizationConfig config, Map<String, String> headers) {
    StringBuilder result = new StringBuilder();
    String[] pairs = formData.split("&");

    for (int i = 0; i < pairs.length; i++) {
      String[] keyValue = pairs[i].split("=", 2);

      if (keyValue.length == 2) {
        String key = keyValue[0];
        String value = keyValue[1];

        // 应用字段名称规则
        SanitizationRule matchedRule = findMatchingFieldRule(key, config);
        if (matchedRule != null) {
          value = applySanitizationRule(value, matchedRule, config);
        } else {
          // 应用模式规则
          value = sanitizeByPatterns(value, config, headers);
        }

        result.append(key).append("=").append(value);
      } else {
        result.append(pairs[i]);
      }

      if (i < pairs.length - 1) {
        result.append("&");
      }
    }

    return result.toString();
  }

  /** 基于模式的脱敏 */
  private String sanitizeByPatterns(
      String text, SanitizationConfig config, Map<String, String> headers) {
    String result = text;

    // 应用模式规则
    for (SanitizationRule rule : getEnabledRules(config, SanitizationRule.RuleType.PATTERN)) {
      if (rule.getPattern() != null) {
        Pattern pattern = getCompiledPattern(rule.getPattern());
        if (pattern != null) {
          result = pattern.matcher(result).replaceAll(applySanitizationRule("", rule, config));
        }
      }
    }

    return result;
  }

  /** 查找匹配的字段规则 */
  private SanitizationRule findMatchingFieldRule(String fieldName, SanitizationConfig config) {
    for (SanitizationRule rule : getEnabledRules(config, SanitizationRule.RuleType.FIELD_NAME)) {
      if (rule.getFieldNames() != null) {
        for (String ruleFieldName : rule.getFieldNames()) {
          if (fieldName.toLowerCase().contains(ruleFieldName.toLowerCase())) {
            return rule;
          }
        }
      }
    }
    return null;
  }

  /** 应用脱敏规则 */
  private String applySanitizationRule(
      String value, SanitizationRule rule, SanitizationConfig config) {
    String maskValue = rule.getMaskValue() != null ? rule.getMaskValue() : "****";

    // 处理格式保留
    if (rule.isPreserveFormat() && value.contains("@")) {
      // 邮箱格式保留
      String domain = value.substring(value.indexOf("@"));
      maskValue = maskValue + domain;
    }

    // 添加标记
    if (config.isMarkersEnabled() && rule.getMarkerType() != null) {
      String markerFormat = config.getMarkerFormat() != null ? config.getMarkerFormat() : "BRACKET";
      maskValue = formatWithMarker(rule.getMarkerType(), maskValue, markerFormat);
    }

    return maskValue;
  }

  /** 添加标记格式 */
  private String formatWithMarker(String type, String value, String format) {
    switch (format.toUpperCase()) {
      case "PREFIX":
        return type + ":" + value;
      case "XML":
        return "<SENSITIVE type=\"" + type + "\">" + value + "</SENSITIVE>";
      case "BRACKET":
      default:
        return "[SENSITIVE:" + type + "]" + value;
    }
  }

  /** 获取启用的规则列表 */
  private List<SanitizationRule> getEnabledRules(
      SanitizationConfig config, SanitizationRule.RuleType type) {
    List<SanitizationRule> enabledRules = new ArrayList<>();
    if (config.getRules() != null) {
      for (SanitizationRule rule : config.getRules()) {
        if (rule.isEnabled() && rule.getType() == type) {
          enabledRules.add(rule);
        }
      }
    }
    // 按优先级排序
    enabledRules.sort(Comparator.comparingInt(SanitizationRule::getPriority));
    return enabledRules;
  }

  /** 检查规则是否适用于指定的内容类型 */
  private boolean isRuleApplicable(SanitizationRule rule, String contentType) {
    if (rule.getContentTypes() == null || rule.getContentTypes().isEmpty()) {
      return true; // 没有限制内容类型
    }

    if (contentType == null) {
      return false;
    }

    String lowerContentType = contentType.toLowerCase();
    for (String supportedType : rule.getContentTypes()) {
      if (lowerContentType.contains(supportedType.toLowerCase())) {
        return true;
      }
    }
    return false;
  }

  /** 获取编译后的正则表达式模式 */
  private Pattern getCompiledPattern(String patternStr) {
    return patternCache.computeIfAbsent(
        patternStr,
        p -> {
          try {
            return Pattern.compile(p);
          } catch (Exception e) {
            logger.warn("Invalid regex pattern: {}", p);
            return null;
          }
        });
  }

  private String getContentType(Map<String, String> headers) {
    if (headers == null)
      return null;
    return headers.get("content-type") != null
        ? headers.get("content-type")
        : headers.get("Content-Type") != null
            ? headers.get("Content-Type")
            : headers.get("CONTENT-TYPE");
  }

  private boolean isJsonContent(String contentType) {
    return contentType != null && contentType.toLowerCase().contains("json");
  }

  private boolean isXmlContent(String contentType) {
    return contentType != null
        && (contentType.toLowerCase().contains("xml")
            || contentType.toLowerCase().contains("soap"));
  }

  private boolean isFormUrlEncoded(String contentType) {
    return contentType != null && contentType.toLowerCase().contains("form-urlencoded");
  }

  /** 关闭资源 */
  public void shutdown() {
    // 不需要在这里停止规则管理器，因为它是全局单例
    // 由 DynamicSanitizationRuleInstaller 管理生命周期
  }
}
