plugins {
   `java-library`
   id("org.hypertrace.publish-maven-central-plugin")
}

val versions: Map<String, String> by extra

dependencies {
   api("io.opentelemetry:opentelemetry-api:${versions["opentelemetry"]}")
   api(project(":javaagent-core"))
   compileOnly("com.google.auto.service:auto-service-annotations:1.0")
   implementation("org.slf4j:slf4j-api:${versions["slf4j"]}")
   implementation("com.fasterxml.jackson.core:jackson-databind:2.16.0")
   annotationProcessor("com.google.auto.service:auto-service:1.0")
   
   testImplementation("org.junit.jupiter:junit-jupiter-api:5.9.2")
   testImplementation("org.junit.jupiter:junit-jupiter-engine:5.9.2")
   testImplementation("org.mockito:mockito-core:4.11.0")
   testImplementation("org.mockito:mockito-junit-jupiter:4.11.0")
}
